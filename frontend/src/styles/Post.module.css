.post {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.post::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-purple), var(--accent-blue));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.post:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.post:hover::before {
  opacity: 1;
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  background: rgba(255, 255, 255, 0.5);
}

.authorInfo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.authorInfo:hover {
  transform: scale(1.02);
}

.authorAvatar {
  border-radius: 50%;
  object-fit: cover;
  margin-right: 1rem;
  border: 2px solid var(--primary-200);
  transition: border-color 0.3s ease;
}

.authorInfo:hover .authorAvatar {
  border-color: var(--primary-400);
}

.authorAvatarPlaceholder {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: 1rem;
  border: 2px solid var(--primary-200);
  transition: all 0.3s ease;
  font-size: 1rem;
}

.authorInfo:hover .authorAvatarPlaceholder {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  border-color: var(--primary-400);
}

.authorName {
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
  color: var(--foreground);
  background: linear-gradient(135deg, var(--foreground), var(--primary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.postTime {
  font-size: 0.85rem;
  color: var(--foreground-muted);
  margin: 0;
  font-weight: 500;
}

.postActions {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdownToggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.3rem;
  color: var(--foreground-muted);
  padding: 0.75rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.dropdownToggle::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-500), var(--accent-purple));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dropdownToggle:hover {
  color: var(--primary-600);
  transform: scale(1.1);
}

.dropdownToggle:hover::before {
  opacity: 0.1;
}

.dropdownToggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdownMenu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 160px;
  z-index: 1000;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
  transform-origin: top right;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dropdownItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem 1.25rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--foreground);
  transition: all 0.2s ease;
  font-weight: 500;
}

.dropdownItem:hover {
  background: rgba(16, 185, 129, 0.1);
  color: var(--primary-600);
  transform: translateX(4px);
}

.dropdownItem:first-child {
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.dropdownItem:last-child {
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.postContent {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.3);
}

.postText {
  margin: 0 0 1rem;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 1rem;
  line-height: 1.7;
  color: var(--foreground);
}

.postImage {
  position: relative;
  width: 100%;
  height: 350px;
  margin-top: 1rem;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.postImage:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

/* Style for GIF images that use regular img tag */
.postImageElement {
  border-radius: var(--radius-lg);
  max-width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.postImageElement:hover {
  transform: scale(1.05);
}

.postFooter {
  padding: 0 1.5rem 1.5rem;
  background: rgba(255, 255, 255, 0.3);
}

.postStats {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid var(--gray-200);
  font-size: 0.9rem;
  color: var(--foreground-muted);
  font-weight: 500;
}

.postButtons {
  display: flex;
  justify-content: space-around;
  padding: 1rem 0;
  border-bottom: 1px solid var(--gray-200);
  gap: 0.5rem;
}

.postButton {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  color: var(--foreground-muted);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  flex: 1;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.postButton::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--primary-500), var(--accent-purple));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.postButton:hover {
  color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.postButton:hover::before {
  opacity: 0.1;
}

.postButton.liked {
  color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}

.postButton.liked::before {
  background: linear-gradient(135deg, var(--error), #ff6b6b);
  opacity: 0.1;
}

.commentsSection {
  margin-top: 1rem;
}

.commentForm {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.commentInputContainer {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.commentActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.commentImageUpload {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f2f5;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.commentImageUpload:hover {
  background-color: #e4e6eb;
}

.commentImagePreview {
  position: relative;
  margin-top: 0.5rem;
  max-width: 200px;
}

.commentPreviewImage {
  width: 100%;
  max-height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.removeCommentImage {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.commentImageContainer {
  margin-top: 0.5rem;
}

.commentImage {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.commentInput {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 20px;
  background-color: #f0f2f5;
  color: #050505; /* Explicitly set text color */
}

.commentInput:focus {
  outline: none;
  border-color: #10b981;
}

.commentsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.noComments {
  text-align: center;
  color: #65676b;
  font-size: 0.9rem;
  padding: 1rem 0;
}

.comment {
  display: flex;
  gap: 0.75rem;
}

.commentAuthor {
  text-decoration: none;
  color: inherit;
}

.commentAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.commentAvatarPlaceholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.8rem;
}

.commentContent {
  flex: 1;
}

.commentBubble {
  background-color: #f0f2f5;
  border-radius: 18px;
  padding: 0.75rem 1rem;
  max-width: fit-content;
}

.commentAuthorName {
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.commentText {
  margin: 0;
  font-size: 0.9rem;
  word-break: break-word;
}

.commentMeta {
  display: flex;
  gap: 1rem;
  margin-top: 0.25rem;
  padding-left: 0.5rem;
}

.commentTime {
  font-size: 0.75rem;
  color: #65676b;
}

.deleteCommentButton {
  background: none;
  border: none;
  font-size: 0.75rem;
  color: #65676b;
  cursor: pointer;
  padding: 0;
}

.deleteCommentButton:hover {
  text-decoration: underline;
}

/* Edit form styles */
.editForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.editTextarea {
  width: 100%;
  min-height: 100px;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.95rem;
  resize: vertical;
  background-color: #f8f9fa;
  color: #050505;
}

.editTextarea:focus {
  outline: none;
  border-color: #10b981;
  background-color: white;
}

.editControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.editVisibilitySelect {
  padding: 0.5rem;
  border: 1px solid #dddfe2;
  border-radius: 6px;
  background-color: white;
  color: #050505;
  font-size: 0.9rem;
}

.editVisibilitySelect:focus {
  outline: none;
  border-color: #10b981;
}

.editButtons {
  display: flex;
  gap: 0.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .editControls {
    flex-direction: column;
    align-items: stretch;
  }

  .editButtons {
    justify-content: flex-end;
  }

  .dropdownMenu {
    right: -10px; /* Adjust for mobile screens */
    min-width: 140px;
  }

  .dropdownItem {
    padding: 1rem;
    font-size: 1rem;
  }
}
