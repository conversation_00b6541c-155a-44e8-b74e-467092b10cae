.groupEventsContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #65676b;
}

.createEventSection {
  margin-bottom: 1rem;
}

.createEventForm {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.formHeader h3 {
  margin: 0;
  color: #1c1e21;
  font-size: 1.25rem;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #65676b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background-color: #f0f2f5;
}

.inputGroup {
  margin-bottom: 1rem;
}

.label {
  display: block;
  font-weight: 600;
  color: #1c1e21;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input, .textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
  font-family: inherit;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
}

.textarea {
  resize: vertical;
}

.dateTimeRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f2f5;
}

.eventsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.emptyEvents {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
  color: #65676b;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.emptyEvents p {
  margin: 0 0 0.5rem;
}

.emptyEvents p:last-child {
  margin: 0;
  font-size: 0.9rem;
}

.eventCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.eventCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
}

.eventHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.eventInfo {
  flex: 1;
}

.eventTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1c1e21;
  margin: 0 0 0.5rem;
}

.eventMeta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.eventTime, .eventLocation {
  font-size: 0.9rem;
  color: #65676b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pastEventBadge {
  background-color: #f0f2f5;
  color: #65676b;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.eventDescription {
  color: #1c1e21;
  line-height: 1.5;
  margin: 0 0 1rem;
}

.eventStats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.statNumber {
  font-size: 1.25rem;
  font-weight: 600;
  color: #10b981;
}

.statLabel {
  font-size: 0.8rem;
  color: #65676b;
  margin-top: 0.25rem;
}

.eventActions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.eventHeaderActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.eventManageActions {
  display: flex;
  gap: 0.5rem;
}

.editEventForm {
  padding: 1rem 0;
  border-top: 1px solid #f0f2f5;
  margin-top: 1rem;
}

@media (max-width: 768px) {

  .createEventForm {
    padding: 1rem;
    margin: 0 0 1rem;
    border-radius: 0;
  }

  .dateTimeRow {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column-reverse;
  }

  .formActions button {
    width: 100%;
  }

  .eventCard {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .eventHeader {
    flex-direction: column;
    gap: 0.5rem;
  }

  .eventMeta {
    gap: 0.5rem;
  }

  .eventStats {
    gap: 1rem;
    padding: 0.75rem;
  }

  .eventActions {
    justify-content: stretch;
  }

  .eventActions button {
    flex: 1;
  }

  .eventHeaderActions {
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .eventManageActions {
    flex-direction: column;
    width: 100%;
  }

  .eventManageActions button {
    width: 100%;
  }
}
