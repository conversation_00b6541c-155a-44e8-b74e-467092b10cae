/* Modal Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal Container */
.modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid #f0f2f5;
  margin-bottom: 1rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1c1e21;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #65676b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background-color: #f0f2f5;
}

/* Modal Content */
.content {
  padding: 0 1.5rem 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 100px);
}

/* Confirm Modal Styles */
.confirmContent {
  text-align: center;
}

.message {
  font-size: 1rem;
  color: #1c1e21;
  margin: 0 0 2rem;
  line-height: 1.5;
}

.actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

/* Alert Modal Styles */
.alertContent {
  text-align: center;
}

.alertIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.alertIcon.success {
  background-color: #d1f2eb;
  color: #00875a;
}

.alertIcon.error {
  background-color: #ffebee;
  color: #d32f2f;
}

.alertIcon.warning {
  background-color: #fff3cd;
  color: #856404;
}

.alertIcon.info {
  background-color: #e3f2fd;
  color: #1976d2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .overlay {
    padding: 0.5rem;
  }
  
  .modal {
    max-width: 100%;
    margin: 0;
    border-radius: 8px;
  }
  
  .header {
    padding: 1rem 1rem 0;
  }
  
  .content {
    padding: 0 1rem 1rem;
  }
  
  .title {
    font-size: 1.125rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .actions button {
    width: 100%;
  }
}
