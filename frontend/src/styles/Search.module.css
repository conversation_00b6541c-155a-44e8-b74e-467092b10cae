.searchContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
  min-height: calc(100vh - 80px);
}

.searchHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.searchTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.searchSubtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.searchInputContainer {
  margin-bottom: 2rem;
}

.searchInputWrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.searchInput {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: 50px;
  font-size: 1rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.searchIcon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: var(--text-secondary);
  pointer-events: none;
}

.searchContent {
  min-height: 400px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.searchPrompt,
.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.searchPromptIcon,
.noResultsIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.searchPrompt h3,
.noResults h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.searchPrompt p,
.noResults p {
  font-size: 1rem;
  margin: 0;
  max-width: 400px;
}

.searchResults {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.resultsHeader {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.resultsHeader h3 {
  font-size: 1.3rem;
  color: var(--text-primary);
  margin: 0;
}

.usersList {
  display: grid;
  gap: 1rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .searchContainer {
    padding: 1rem;
  }

  .searchTitle {
    font-size: 2rem;
  }

  .searchSubtitle {
    font-size: 1rem;
  }

  .searchInput {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
    font-size: 0.9rem;
  }

  .searchIcon {
    left: 0.875rem;
    font-size: 1rem;
  }

  .searchPromptIcon,
  .noResultsIcon {
    font-size: 3rem;
  }

  .searchPrompt h3,
  .noResults h3 {
    font-size: 1.3rem;
  }

  .searchPrompt p,
  .noResults p {
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .searchInput {
    background: var(--bg-primary);
    border-color: var(--border-color-dark);
  }

  .searchInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  }
}
