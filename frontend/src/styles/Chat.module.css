.chatContainer {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f0f2f5;
}

.chatSidebar {
  width: 300px;
  background-color: white;
  border-right: 1px solid #dddfe2;
  display: flex;
  flex-direction: column;
}

.sidebarHeader {
  padding: 1rem;
  border-bottom: 1px solid #dddfe2;
}

.sidebarTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.loading, .emptyContacts {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 1rem;
  text-align: center;
  color: #65676b;
}

.contactsList {
  flex: 1;
  overflow-y: auto;
}

.contactItem {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contactItem:hover {
  background-color: #f0f2f5;
}

.activeContact {
  background-color: #e6f2ff;
}

.contactAvatarContainer {
  position: relative;
  margin-right: 0.75rem;
}

.contactAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.contactAvatarPlaceholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.onlineIndicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.contactInfo {
  flex: 1;
}

.contactName {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
}

.contactUsername {
  font-size: 0.8rem;
  color: #65676b;
  margin: 0;
}

.chatMain {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.noChatSelected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #65676b;
  font-size: 1.1rem;
}

.chatHeader {
  padding: 0.75rem 1rem;
  background-color: white;
  border-bottom: 1px solid #dddfe2;
}

.chatHeaderInfo {
  display: flex;
  align-items: center;
}

.headerAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.headerAvatarPlaceholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.headerName {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.headerUsername {
  font-size: 0.8rem;
  color: #65676b;
  margin: 0;
}

.onlineText {
  color: #10b981;
  font-weight: 500;
}

.messagesContainer {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background-color: #f0f2f5;
}

.emptyMessages {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  color: #65676b;
}

.messagesList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.messageItem {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.ownMessage {
  align-self: flex-end;
}

.otherMessage {
  align-self: flex-start;
}

.messageContent {
  padding: 0.75rem 1rem;
  border-radius: 18px;
  word-break: break-word;
}

.ownMessage .messageContent {
  background-color: #10b981;
  color: white;
  border-bottom-right-radius: 4px;
}

.otherMessage .messageContent {
  background-color: white;
  color: #050505;
  border-bottom-left-radius: 4px;
}

.messageTime {
  font-size: 0.7rem;
  color: #65676b;
  margin-top: 0.25rem;
  align-self: flex-end;
}

.optimisticMessage {
  opacity: 0.7;
}

.optimisticMessage .messageContent {
  position: relative;
}

.sendingIndicator {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 0.5rem;
  font-style: italic;
}

.otherMessage .sendingIndicator {
  color: #65676b;
}

.contactAvatarContainer {
  position: relative;
  display: inline-block;
}

.onlineIndicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  z-index: 1;
}

.onlineText {
  color: #10b981;
  font-weight: 500;
}

.typingIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  margin: 0.5rem 0;
  background-color: #f0f2f5;
  border-radius: 1rem;
  max-width: fit-content;
  animation: fadeIn 0.3s ease-in;
}

.typingDots {
  display: flex;
  gap: 0.2rem;
}

.typingDots span {
  width: 6px;
  height: 6px;
  background-color: #65676b;
  border-radius: 50%;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typingDots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typingDots span:nth-child(3) {
  animation-delay: 0s;
}

.typingText {
  font-size: 0.8rem;
  color: #10b981;
  font-style: italic;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.messageForm {
  background-color: white;
  border-top: 1px solid #dddfe2;
}

.emojiBar {
  position: absolute;
  bottom: 60px;
  left: auto;
  z-index: 999;
  background: #f8f9fa;
  border: 1px solid #dddfe2;
  border-radius: 8px;
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 0.25rem;
  width: 260px; /* Adjust to control how many emojis per row */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.emojiBarGroup {
  position: absolute;
  bottom: -275px;
  z-index: 999;
  background: #f8f9fa;
  border: 1px solid #dddfe2;
  border-radius: 8px;
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 0.25rem;
  width: 260px; /* Adjust to control how many emojis per row */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  left: auto;
}

.emojiToggleButton {
  margin-left: auto; /* Pushes it to the right in a flex container */
  background-color: #e0f3ff;
  color: #10b981;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s ease;
}

.emojiToggleButton:hover {
  background-color: #cce8ff;
}

.emojiButton {
  background: none;
  border: none;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emojiButton:hover {
  background: #e4e6ea;
}

.inputContainer {
  display: flex;
  padding: 1rem;
  gap: 0.75rem;
}

.messageInput {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 20px;
  background-color: #f0f2f5;
  color: #050505; /* Explicitly set text color */
}

.messageInput:focus {
  outline: none;
  border-color: #10b981;
}

@media (max-width: 768px) {
  .chatContainer {
    flex-direction: column;
  }

  .chatSidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #dddfe2;
  }

  .contactsList {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .contactItem {
    flex-direction: column;
    text-align: center;
    min-width: 80px;
  }

  .contactAvatar, .contactAvatarPlaceholder {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
