.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
}

.header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.searchContainer {
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
}

.searchInput {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.searchInput:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.selectAllButton {
  background: none;
  border: 1px solid #007bff;
  color: #007bff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.selectAllButton:hover:not(:disabled) {
  background-color: #007bff;
  color: white;
}

.selectAllButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.selectedCount {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.followersList {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.loading {
  padding: 40px;
  text-align: center;
  color: #666;
}

.noFollowers {
  padding: 40px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.followerItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.followerItem:hover {
  background-color: #f8f9fa;
}

.followerItem.selected {
  background-color: #e3f2fd;
}

.followerInfo {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  border: 2px solid #e1e5e9;
}

.followerDetails {
  flex: 1;
}

.username {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
  margin-bottom: 2px;
}

.fullName {
  color: #666;
  font-size: 13px;
}

.checkbox {
  margin-left: 12px;
}

.checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  border-top: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.cancelButton {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancelButton:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

.confirmButton {
  background-color: #007bff;
  border: 1px solid #007bff;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.confirmButton:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .header {
    padding: 15px;
  }
  
  .searchContainer {
    padding: 15px;
  }
  
  .actions {
    padding: 12px 15px;
  }
  
  .followerItem {
    padding: 10px 15px;
  }
  
  .footer {
    padding: 15px;
  }
}
