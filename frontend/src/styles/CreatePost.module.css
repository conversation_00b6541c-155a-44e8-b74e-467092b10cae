.createPostContainer {
  max-width: 700px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.createPostCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.createPostTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #10b981;
  text-align: center;
}

.errorAlert {
  background-color: #ffebe9;
  color: #e41e3f;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(228, 30, 63, 0.3);
}

.createPostForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.userAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.userAvatarPlaceholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.userName {
  font-weight: 600;
}

.contentInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 8px;
  resize: vertical;
  font-family: inherit;
  font-size: 1rem;
  color: #050505; /* Explicitly set text color */
  background-color: #fff; /* Ensure background is white */
}

.contentInput:focus {
  outline: none;
  border-color: #10b981;
}

.imagePreviewContainer {
  position: relative;
  margin-top: 0.5rem;
}

.imagePreview {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.removeImageButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
}

.postOptions {
  background-color: #f0f2f5;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.visibilityOption {
  margin-bottom: 1rem;
}

.visibilityLabel {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.visibilitySelect {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dddfe2;
  border-radius: 4px;
  background-color: white;
  color: #050505; /* Explicitly set text color */
}

.addToPost {
  border-top: 1px solid #dddfe2;
  padding-top: 1rem;
}

.addToPostLabel {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.addToPostOptions {
  display: flex;
  gap: 1rem;
}

.imageUploadLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.imageUploadLabel:hover {
  background-color: #e4e6eb;
}

.imageIcon {
  font-size: 1.25rem;
}

.imageInput {
  display: none;
}

/* File type indicator */
.fileTypeIndicator {
  position: absolute;
  top: 8px;
  right: 40px; /* Position to the left of remove button */
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
}

.fileTypeIndicator span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Privacy Description */
.privacyDescription {
  margin-top: 0.5rem;
}

.privacyNote {
  font-size: 13px;
  color: #666;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #007bff;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Custom Visibility Section */
.customVisibilitySection {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.customVisibilityHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.customVisibilityLabel {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.selectFollowersButton {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.selectFollowersButton:hover {
  background-color: #0056b3;
}

.noFollowersSelected {
  color: #666;
  font-style: italic;
  font-size: 14px;
  text-align: center;
  padding: 1rem;
  background-color: #fff;
  border: 1px dashed #ddd;
  border-radius: 6px;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .customVisibilityHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .selectFollowersButton {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .createPostCard {
    padding: 1rem;
  }
}
