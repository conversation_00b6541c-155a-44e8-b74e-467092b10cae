/* Modal Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal Container */
.modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.closeButton:hover {
  color: #6b7280;
  background-color: #f3f4f6;
}

/* Content */
.content {
  padding: 0 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* Settings */
.setting {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.setting:last-child {
  border-bottom: none;
}

.settingInfo {
  flex: 1;
  margin-right: 1rem;
}

.settingTitle {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.settingDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  flex-shrink: 0;
}

.toggleInput {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggleSlider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.3s;
  border-radius: 24px;
}

.toggleSlider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggleInput:checked + .toggleSlider {
  background-color: #10b981;
}

.toggleInput:checked + .toggleSlider:before {
  transform: translateX(24px);
}

/* Volume Control */
.volumeControl {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
}

.volumeSlider {
  width: 100px;
  height: 4px;
  border-radius: 2px;
  background: #d1d5db;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.volumeSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.volumeSlider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.volumeValue {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  min-width: 35px;
  text-align: right;
}

/* Info Section */
.info {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 8px;
}

.infoTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.infoList {
  font-size: 0.8125rem;
  color: #6b7280;
  margin: 0;
  padding-left: 1rem;
  line-height: 1.5;
}

.infoList li {
  margin-bottom: 0.25rem;
}

/* Footer */
.footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
  .modal {
    margin: 1rem;
    max-width: none;
  }

  .header,
  .content,
  .footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .setting {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .settingInfo {
    margin-right: 0;
  }

  .volumeControl {
    width: 100%;
    justify-content: space-between;
  }

  .volumeSlider {
    flex: 1;
    max-width: 150px;
  }
}
