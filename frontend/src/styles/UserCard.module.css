.userCard {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e4e6ea;
  transition: all 0.2s ease;
  gap: 1rem;
}

.userCard:hover {
  border-color: #10b981;
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.1);
}

.userLink {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  text-decoration: none;
  color: inherit;
}

.userAvatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.userInfo {
  flex: 1;
  min-width: 0; /* Allow text to truncate */
}

.userNameRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.userName {
  font-size: 1rem;
  font-weight: 600;
  color: #1c1e21;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.privacyTag {
  font-size: 0.625rem;
  font-weight: 500;
  padding: 0.125rem 0.25rem;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
}

.privateTag {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.publicTag {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.userUsername {
  font-size: 0.875rem;
  color: #65676b;
  margin: 0 0 0.25rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userBio {
  font-size: 0.875rem;
  color: #65676b;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.userActions {
  flex-shrink: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .userCard {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .userAvatar {
    width: 40px;
    height: 40px;
  }

  .userName {
    font-size: 0.9rem;
  }

  .userUsername,
  .userBio {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .userCard {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .userInfo {
    min-width: 0;
  }

  .userName,
  .userUsername {
    max-width: 150px;
  }
}
