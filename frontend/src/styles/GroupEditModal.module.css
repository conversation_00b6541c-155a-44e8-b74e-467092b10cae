.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form {
  padding: 0 24px 24px 24px;
}

.formGroup {
  margin-bottom: 24px;
}

.label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.input,
.textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled,
.textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

/* Cover Photo Section */
.coverPhotoSection {
  margin-bottom: 24px;
}

.coverPhotoContainer {
  margin-top: 8px;
}

.coverPhotoPreview {
  position: relative;
  margin-bottom: 12px;
}

.coverPhotoImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.removeCoverButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.removeCoverButton:hover {
  background-color: rgba(220, 38, 38, 0.9);
}

.removeCoverButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.coverPhotoPlaceholder {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 12px;
}

.fileInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: border-color 0.2s;
}

.fileInput:hover {
  border-color: #9ca3af;
}

.fileInput:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Privacy Options */
.privacyOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radioOption {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.radioOption:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.radioOption input[type="radio"] {
  margin: 0;
  margin-top: 2px;
}

.radioOption input[type="radio"]:disabled {
  cursor: not-allowed;
}

.radioContent {
  flex: 1;
}

.radioTitle {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.radioDescription {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Actions */
.actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 16px 16px 0 16px;
    margin-bottom: 16px;
  }

  .modalHeader h2 {
    font-size: 1.25rem;
  }

  .form {
    padding: 0 16px 16px 16px;
  }

  .formGroup {
    margin-bottom: 20px;
  }

  .actions {
    flex-direction: column-reverse;
    margin-top: 24px;
    padding-top: 16px;
  }

  .privacyOptions {
    gap: 8px;
  }

  .radioOption {
    padding: 12px;
  }
}
