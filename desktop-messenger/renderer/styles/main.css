/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow: hidden;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Loading screen */
.loading-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #fff;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen management */
.screen {
    height: 100vh;
    width: 100vw;
}

/* Login screen */
.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.login-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    min-width: 400px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.form-group input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.login-footer {
    text-align: center;
    margin-top: 20px;
}

.login-footer a {
    color: #fff;
    text-decoration: underline;
}

/* Chat screen */
#chat-screen {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background-color: #2c3e50;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #34495e;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.user-details h3 {
    font-size: 16px;
    margin-bottom: 2px;
}

.user-status {
    font-size: 12px;
    color: #95a5a6;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

.search-container {
    padding: 15px 20px;
    border-bottom: 1px solid #34495e;
}

.search-input {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 20px;
    background-color: #34495e;
    color: white;
    font-size: 14px;
}

.search-input::placeholder {
    color: #95a5a6;
}

.search-input:focus {
    outline: none;
    background-color: #3c5a78;
}

.contacts-list {
    flex: 1;
    overflow-y: auto;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 1px solid #34495e;
    transition: background-color 0.2s;
}

.contact-item:hover {
    background-color: #34495e;
}

.contact-item.active {
    background-color: #3498db;
}

.contact-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
    position: relative;
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-size: 16px;
    margin-bottom: 2px;
}

.contact-preview {
    font-size: 13px;
    color: #95a5a6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.online-indicator {
    width: 12px;
    height: 12px;
    background-color: #2ecc71;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 2px solid #2c3e50;
}

/* Main chat area */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #95a5a6;
    text-align: center;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    background-color: #fff;
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.contact-details h3 {
    font-size: 18px;
    margin-bottom: 2px;
    color: #2c3e50;
}

.contact-status {
    font-size: 14px;
    color: #95a5a6;
}

/* Messages */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.message.own {
    align-self: flex-end;
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.other {
    align-self: flex-start;
    background-color: #e9ecef;
    color: #333;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
}

.message.sending {
    opacity: 0.6;
}

.message.sending::after {
    content: " (sending...)";
    font-size: 11px;
    opacity: 0.7;
}

/* Message input */
.message-input-container {
    border-top: 1px solid #ecf0f1;
    background-color: #fff;
}

.offline-indicator {
    background-color: #f39c12;
    color: white;
    padding: 10px 20px;
    text-align: center;
    font-size: 14px;
}

.message-form {
    padding: 20px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
}

.message-input:focus {
    border-color: #007bff;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-primary:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-icon {
    padding: 8px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid #ddd;
}

.btn-icon:hover {
    background-color: #f8f9fa;
}

/* Emoji picker */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 20px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    max-width: 300px;
    z-index: 1000;
}

.emoji-btn {
    padding: 8px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 18px;
}

.emoji-btn:hover {
    background-color: #f8f9fa;
}

/* Error messages */
.error-message {
    background-color: #dc3545;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin-top: 15px;
    text-align: center;
}

/* Offline notification */
.offline-notification {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #f39c12;
    color: white;
    padding: 10px;
    text-align: center;
    z-index: 1000;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
