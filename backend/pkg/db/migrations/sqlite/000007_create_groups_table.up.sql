CREATE TABLE IF NOT EXISTS groups (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    creator_id TEXT NOT NULL,
    cover_photo TEXT,
    privacy TEXT NOT NULL CHECK (privacy IN ('public', 'private')),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
);
