version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api
      - NEXT_PUBLIC_SOCKET_URL=ws://localhost:8080/ws
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./uploads:/app/uploads
    environment:
      - PORT=8080
      - DB_PATH=/app/data/social_network.db
      - MIGRATIONS_PATH=/app/pkg/db/migrations/sqlite
    restart: unless-stopped
